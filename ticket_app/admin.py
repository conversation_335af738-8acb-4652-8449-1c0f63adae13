import csv
import datetime
import io
import zipfile

import openpyxl
from django.contrib import admin, messages
from django.db.models import Sum, Count
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import render, redirect
from django.urls import path
from django.utils.html import format_html
from django.utils.safestring import mark_safe

from .models import *
from django import forms
from ticket_app.models import Ticket
from .tasks import extract_img_text, bill_enterprise_yardage
from django.contrib import admin
from django.utils import timezone
from timepunch.models import TimePunch
from datetime import timedelta
import logging
logger = logging.getLogger('django')

def export_to_csv(modeladmin, queryset):
    opts = modeladmin.model._meta
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment;' 'filename={}.csv'.format(opts.verbose_name)
    writer = csv.writer(response)
    fields = [field for field in opts.get_fields() if not field.many_to_many and not field.one_to_many]
    fields.append(opts.model.total_yards)
    fields.append(opts.model.total_yards_per_truck)
    fields.append(opts.model.total_yards_together)
    # Write a first row with header information
    writer.writerow([field.verbose_name for field in fields])
    # Write data rows
    for obj in queryset:
        data_row = []
        for field in fields:
            value = getattr(obj, field.name)
            if isinstance(value, datetime.datetime):
                value = value.strftime('%d/%m/%Y')
            data_row.append(value)
        # Append the total_yards value to the data row
        total_yards = getattr(obj, 'total_yards')
        data_row.append(total_yards)
        writer.writerow(data_row)

    return response


export_to_csv.short_description = 'Export to CSV'  # short description

def download_tickets_for_area(modeladmin, request, queryset):
    """
    Admin action to zip and download all ticket images for the selected debris areas
    belonging to the current user.
    """
    # Only allow one debris area at a time
    if queryset.count() != 1:
        modeladmin.message_user(request, "Please select exactly one debris area.", level=messages.ERROR)
        return
    debris_area = queryset.first()
    # Gather tickets: superusers get all in the area, others only their own
    if request.user.is_superuser:
        tickets = Ticket.objects.filter(
            debris_area=debris_area
        ).select_related('image_id')
    else:
        tickets = Ticket.objects.filter(
            author=request.user,
            debris_area=debris_area
        ).select_related('image_id')
    count = tickets.count()
    logger.info(f"[download_tickets_for_area] Found {count} tickets for debris_area {debris_area.id}")
    buf = io.BytesIO()
    logger.info("[download_tickets_for_area] Beginning to assemble ZIP")
    file_list = []
    with zipfile.ZipFile(buf, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for ticket in tickets:
            img = ticket.image_id
            logger.info(f"[download_tickets_for_area] Processing ticket_no={ticket.ticket_no}, image_id={getattr(img, 'id', None)}, images.name={getattr(img.images, 'name', None)}")
            if img and img.images:
                path = img.images.path
                if os.path.exists(path):
                    ext = os.path.splitext(path)[1]
                    arcname = f"{ticket.ticket_no}{ext}"
                    zipf.write(path, arcname)
                    file_list.append(arcname)
    logger.info(f"[download_tickets_for_area] Finished ZIP; files added: {file_list}")
    buf.seek(0)
    filename = f"tickets_{debris_area.id}.zip"
    response = HttpResponse(buf.read(), content_type='application/zip')
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    return response

download_tickets_for_area.short_description = "Download tickets for selected debris area"


class DateRangeForm(forms.Form):
    start_date = forms.DateField(widget=forms.DateInput(attrs={'type': 'date'}))
    end_date = forms.DateField(widget=forms.DateInput(attrs={'type': 'date'}))


class BillingDateRangeForm(forms.Form):
    start_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        required=True,
        label="Start Date"
    )
    end_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        required=True,
        label="End Date"
    )

@admin.register(TicketUser)
class TicketUserAdmin(admin.ModelAdmin):
    change_list_template = 'admin/timepunch_changelist.html'
    list_display = ('email', 'first_name', 'last_name', 'subscription_date', 'price_per_yardage')
    actions = ['export_user_hours', 'trigger_billing']

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.filter(role='master')

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('export-hours/', self.export_hours),
            path('bill-users/', self.admin_site.admin_view(self.bill_users), name='bill_users'),
        ]
        return custom_urls + urls

    def export_hours(self, request):
        if request.method == 'POST':
            form = DateRangeForm(request.POST)
            if form.is_valid():
                response = self.export_user_hours(request, form.cleaned_data['start_date'], form.cleaned_data['end_date'])
                return response
        else:
            form = DateRangeForm()

        return render(request, 'admin/date_range_form.html', {'form': form})

    def export_user_hours(self, request, start_date, end_date):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="user_hours.csv"'
        writer = csv.writer(response)
        writer.writerow(['User', 'Total Hours'])

        queryset = TicketUser.objects.all()
        for user in queryset:
            time_punches = TimePunch.objects.filter(User=user, clocked_out_time__isnull=False, clocked_in_time__range=[start_date, end_date])
            total_duration = timedelta()

            for punch in time_punches:
                duration = punch.clocked_out_time - punch.clocked_in_time
                total_duration += duration

            total_hours = total_duration.total_seconds() / 3600
            total_hours_rounded = round(total_hours, 2)  # Round to 2 decimal places
            writer.writerow([user.first_name + " " + user.last_name, total_hours_rounded])
        return response

    def bill_users(self, request):
        """
        Display a form to select the date range and trigger billing.
        """
        if request.method == 'POST':
            form = BillingDateRangeForm(request.POST)
            if form.is_valid():
                start_date = form.cleaned_data['start_date']
                end_date = form.cleaned_data['end_date']

                # Trigger the billing task
                task_result = bill_enterprise_yardage.delay(
                    start_date=start_date.isoformat(),
                    end_date=end_date.isoformat()
                )

                self.message_user(
                    request,
                    f"Billing task has been initiated for the period {start_date} to {end_date}. Task ID: {task_result.id}",
                    level=messages.SUCCESS
                )
                return redirect('..')
        else:
            form = BillingDateRangeForm()

        return render(request, 'admin/ticket_app/ticket/bill_users_form.html', {'form': form})

    def trigger_billing(self, request, queryset):
        """
        Admin action to trigger billing for a specific user.
        """
        return redirect('admin:bill_users')

    trigger_billing.short_description = "Bill users for a date range"

@admin.register(SubUser)
class SubUserAdmin(admin.ModelAdmin):
    list_display = ('username', 'first_name', 'last_name', 'master_account')
    search_fields = ('username', 'first_name', 'last_name', 'master_account__email')

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.filter(role='sub')  # Only show sub-users

class ProjectAdmin(admin.ModelAdmin):
    list_display = ('project_name', 'user', 'slug')  # Customize based on your needs
    search_fields = ('project_name',)

class FailedTicketImageAdmin(admin.ModelAdmin):
    list_display = ['id', 'thumbnail_image', 'upload_date', 'author', 'failure_status', 'retry_extraction']
    list_filter = ['extraction_failed', 'author', 'upload_date']
    search_fields = ['author__email', 'author__first_name', 'author__last_name']
    actions = ['retry_extraction_batch', 'mark_as_extracted', 'delete_selected']

    def get_queryset(self, request):
        """Only show failed ticket images"""
        qs = super().get_queryset(request)
        return qs.filter(extracted=False, extraction_failed=True)

    def thumbnail_image(self, obj):
        """Display thumbnail of the ticket image"""
        if obj.images:
            return format_html(
                '<img src="{}" width="500" height="500" style="object-fit: cover;"/>',
                obj.images.url
            )
        return "No image"

    thumbnail_image.short_description = 'Image'

    def failure_status(self, obj):
        """Display the failure status with styling"""
        if obj.extraction_failed:
            return format_html(
                '<span style="color: red; font-weight: bold;">Failed</span>'
            )
        return format_html(
            '<span style="color: green;">Pending</span>'
        )

    failure_status.short_description = 'Status'

    def retry_extraction(self, obj):
        """Add a retry button for each row"""
        return format_html(
            '<a class="button" href="{}">Retry Extraction</a>',
            f'/admin/ticket_app/ticketimage/{obj.pk}/retry-extraction/'
        )

    retry_extraction.short_description = 'Retry'

    def retry_extraction_batch(self, request, queryset):
        """Batch action to retry extraction for multiple images"""
        image_ids = list(queryset.values_list('id', flat=True))
        task = extract_img_text.delay(image_ids, request.user.id)
        self.message_user(
            request,
            f"Extraction retry initiated for {len(image_ids)} images. Task ID: {task.id}",
            messages.SUCCESS
        )

    retry_extraction_batch.short_description = "Retry extraction for selected images"

    def mark_as_extracted(self, request, queryset):
        """Mark selected images as extracted without processing"""
        updated = queryset.update(extracted=True, extraction_failed=False)
        self.message_user(
            request,
            f"{updated} images marked as extracted successfully.",
            messages.SUCCESS
        )

    mark_as_extracted.short_description = "Mark selected as extracted"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path(
                '<path:object_id>/retry-extraction/',
                self.admin_site.admin_view(self.retry_single_extraction),
                name='ticketimage-retry-extraction',
            ),
        ]
        return custom_urls + urls

    def retry_single_extraction(self, request, object_id):
        """Handle single image extraction retry"""
        try:
            image = self.get_queryset(request).get(pk=object_id)
            task = extract_img_text.delay([image.id], request.user.id)
            self.message_user(
                request,
                f"Extraction retry initiated for image {image.id}. Task ID: {task.id}",
                messages.SUCCESS
            )
        except TicketImage.DoesNotExist:
            self.message_user(
                request,
                f"Image {object_id} not found or is not a failed image.",
                messages.ERROR
            )

        return HttpResponseRedirect("../")

    class Media:
        css = {
            'all': ('admin/css/custom_admin.css',)
        }

class TicketImageAdminForm(forms.ModelForm):
    class Meta:
        model = TicketImage
        fields = ['images', 'debris_area', 'image_date', 'extracted']

        widgets = {
            'images': forms.FileInput(),
        }


def generate_registration_links(modeladmin, request, queryset):
    for _ in range(queryset.count()):
        RegistrationToken.objects.create()

generate_registration_links.short_description = "Generate selected number of registration links"


@admin.register(RegistrationToken)
class RegistrationTokenAdmin(admin.ModelAdmin):
    list_display = ['token', 'is_used', 'created_at']
    readonly_fields = ['token', 'created_at']

    # Overriding the save_model to set the initial value of is_used to False
    def save_model(self, request, obj, form, change):
        if not change:
            obj.is_used = False
        super().save_model(request, obj, form, change)

    actions = [generate_registration_links]

    # Change the default behavior to select 1 instead of all
    def get_actions(self, request):
        actions = super().get_actions(request)
        if 'delete_selected' in actions:
            del actions['delete_selected']
        return actions



def duplicate_ticket(modeladmin, request, queryset, *args, **kwargs):
    for ticket in queryset:
        ticket.pk = None  # Set primary key to None to create a new instance
        ticket.save()
duplicate_ticket.short_description = "Duplicate selected tickets"

class AdminTicketImages(admin.ModelAdmin):
    form = TicketImageAdminForm
    list_display = ['ticket_image', 'upload_date', 'extracted', 'author']
    list_filter = ['extracted', 'truck', 'author', 'upload_date']
    actions = ['mark_as_extracted']

    @admin.action(description="Mark selected images as extracted")
    def mark_as_extracted(self, request, queryset):
        updated = queryset.update(extracted=True)
        self.message_user(request, f"{updated} images marked as extracted successfully.")

    def ticket_image(self, obj):
        return mark_safe('<img src="{url}" width="500" height="500" />'.format(
            url=obj.images.url,
        ))

    def save_model(self, request, obj, form, change):
        """
        Override save_model to prevent images from being created twice.
        Only save the image if it doesn't already exist.
        """
        if not change:
            for f in request.FILES.getlist('images'):
                # Generate image hash to check for duplication
                image_hash = hashlib.md5(f.read()).hexdigest()
                f.seek(0)  # Reset file pointer

                # Check if image with same hash already exists
                duplicate = TicketImage.objects.filter(image_hash=image_hash, extraction_failed=False).exists()
                if not duplicate:
                    obj.images = f
                    obj.image_hash = image_hash
                    obj.author = request.user
                    super().save_model(request, obj, form, change)

        # If the instance is being updated, no need to recreate images
        elif change:
            super().save_model(request, obj, form, change)

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if request.user.is_superuser:
            return qs
        return qs.filter(author=request.user)


@admin.action(description="Export Trucks to Excel")
def export_trucks_to_excel(modeladmin, request, queryset):
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Trucks"
    ws.append(["Truck Number", "Truck Capacity", "Sub‑Contractor", "Debris Areas"])

    for truck in queryset.select_related('sub_contractor').prefetch_related('sub_contractor__project'):
        areas = truck.sub_contractor.project.values_list('area', flat=True)
        ws.append([
            truck.truck_number,
            truck.truck_capacity,
            truck.sub_contractor.sub_contractor_name,
            ", ".join(areas) if areas else "—",
        ])

    response = HttpResponse(
        content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    )
    response["Content-Disposition"] = 'attachment; filename="trucks.xlsx"'
    wb.save(response)
    return response

class TruckAdmin(admin.ModelAdmin):
    list_display = ('truck_number', 'sub_contractor', 'truck_capacity', 'user', 'get_debris_areas')
    search_fields = ('truck_number', 'sub_contractor__name')
    list_filter = ('sub_contractor', 'user',)
    actions = [export_trucks_to_excel, 'Export to Excel']

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.prefetch_related('tickets__debris_area')  # Adjust based on related name

    def get_debris_areas(self, obj):
        debris_areas = obj.tickets.values_list('debris_area__area', flat=True).distinct()  # Adjust `tickets` if needed
        return ", ".join(debris_areas) if debris_areas else "No debris areas"

    get_debris_areas.short_description = "Debris Areas"



class UpdateTicketsForm(forms.Form):
    questionable = forms.ChoiceField(
        choices=[(True, 'Yes'), (False, 'No')],
        widget=forms.Select(),
        required=False
    )
    debris_type = forms.ModelChoiceField(
        queryset=DebrisType.objects.all(),
        required=False
    )

@admin.action(description="Update questionable status and debris type for selected tickets")
def update_tickets_status(modeladmin, request, queryset):
    if 'apply' in request.POST:
        form = UpdateTicketsForm(request.POST)
        if form.is_valid():
            questionable = form.cleaned_data.get('questionable')
            debris_type = form.cleaned_data.get('debris_type')

            # Debugging: Print the received data
            print(f"Form Values: Questionable={questionable}, Debris Type={debris_type}")

            updated_count = 0
            for ticket in queryset:
                print(f"Before Update - Ticket ID: {ticket.ticket_id}, Questionable: {ticket.questionable}, Debris Type: {ticket.debris_type}")

                if questionable is not None:
                    ticket.questionable = questionable == 'True'
                if debris_type:
                    ticket.debris_type = debris_type

                ticket.save()

                print(f"After Update - Ticket ID: {ticket.ticket_id}, Questionable: {ticket.questionable}, Debris Type: {ticket.debris_type}")
                updated_count += 1

            modeladmin.message_user(
                request,
                f"Successfully updated {updated_count} tickets.",
                messages.SUCCESS
            )
            return redirect(request.get_full_path())
        else:
            print(f"Form Errors: {form.errors}")
            modeladmin.message_user(
                request,
                "Form submission failed. Please correct the errors and try again.",
                messages.ERROR
            )
    else:
        form = UpdateTicketsForm()

    # Render form
    return render(
        request,
        'admin/update_tickets_form.html',
        context={'form': form, 'tickets': queryset}
    )

class TicketAdmin(admin.ModelAdmin):
    raw_id_fields = ('Truck_No', 'hauling_behind', 'debris_area', 'image_id', 'author')
    # list_select_related = ('Truck_No', 'hauling_behind', 'debris_area', 'image_id', 'author')
    list_display = (
        'Truck_No', 'ticket_id', 'ticket_no', 'load_call', 'hauling_behind', 'Ticket_date',
        'debris_area', 'total_yards', 'questionable'
    )
    actions = [export_to_csv, 'Export to CSV', duplicate_ticket, update_tickets_status]
    list_filter = ('Truck_No', 'Ticket_date', 'ticket_no', 'author', 'debris_area', 'Contractor')

    # def display_ticket_image(self, obj):
    #     """
    #     Display the ticket image as a large image on the edit page.
    #     """
    #     if obj.image_id and obj.image_id.images:
    #         return format_html(
    #             '<img src="{}" style="width: 100%; height: auto;" />',
    #             obj.image_id.images.url
    #         )
    #     return "No image available"
    #
    # display_ticket_image.short_description = "Ticket Image"

    def changelist_view(self, request, extra_context=None):
        """
        Override the changelist view to include totals for unbilled tickets.
        """
        stats = Ticket.objects.filter(billed=False).aggregate(
            total_unbilled_yardage=Sum('total_yardage'),
            total_unbilled_tickets=Count('ticket_id')
        )
        total_unbilled_yardage = stats['total_unbilled_yardage'] or 0
        total_unbilled_tickets = stats['total_unbilled_tickets'] or 0

        # Add totals to extra_context
        extra_context = extra_context or {}
        extra_context['total_unbilled_yardage'] = total_unbilled_yardage
        extra_context['total_unbilled_tickets'] = total_unbilled_tickets

        return super().changelist_view(request, extra_context=extra_context)





@admin.register(UserTaskLock)
class UserTaskLockAdmin(admin.ModelAdmin):
    list_display = ['user', 'timestamp']
    search_fields = ['user__email', 'user__username']  #

class HangarTicketAdmin(admin.ModelAdmin):
    list_display = ('Truck_No', 'ticket_id', 'ticket_no', 'measure', 'Ticket_date', 'questionable', 'ticket_image')
    actions = [export_to_csv, 'Export to CSV']
    list_filter = ('Truck_No', 'Ticket_date')

    def save_model(self, request, obj, form, change):
        obj.author = request.user
        obj.save()

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if request.user.is_superuser:
            return qs
        return qs.filter(author=request.user.id)

    def ticket_image(self, obj):
        try:
            ticket_image = obj.image_id
            if ticket_image:
                return mark_safe('<img src="{url}" width="100" height="100" />'.format(
                    url=ticket_image.images.url,
                ))
        except TicketImage.DoesNotExist:
            pass
        return None

    ticket_image.short_description = 'Ticket Image'

class ClientAdmin(admin.ModelAdmin):
    list_display = ('contractor_name', 'phone_number', 'fema_number')
    list_filter = ('contractor_name', 'phone_number', 'fema_number')
    # actions = [Client.generate_pdf_report]

    def change_view(self, request, object_id, form_url='', extra_context=None):
        extra_context = extra_context or {}
        extra_context['show_button'] = True
        return super().change_view(request, object_id, form_url, extra_context=extra_context)


class EmployeeAdmin(admin.ModelAdmin):
    list_display = ('first_name', 'last_name', 'pay_rate', 'has_insurance')


class UserAdminSite(admin.AdminSite):
    site_header = "Ticket Pros"
    site_title = "Ticket Pros Dashboard"
    index_title = "Ticket Pros"




@admin.action(description="Extract Images Text Celery")
def extract_text(self, request, queryset):
    # Batch all selected images into a single task invocation
    image_ids = list(queryset.values_list('pk', flat=True))
    first_image = queryset.first()
    if not first_image:
        return

    user = first_image.author
    if user:
        extract_img_text.delay(image_ids, user.id, None, True)


class TimePunchAdmin(admin.ModelAdmin):
    list_display = ['User', 'clocked_in_time', 'clocked_out_time', 'latitude', 'longitude']

    actions = ['calculate_weekly_hours']

# Inline for SubcontractorProject
class SubcontractorProjectInline(admin.TabularInline):
    model = SubcontractorProject
    extra = 1  # Display one extra blank form
    autocomplete_fields = ['project']  # Improves usability with large datasets
    search_fields = ('project_name',)

class SubContractorAdmin(admin.ModelAdmin):
    list_display = ('sub_contractor_name', 'phone_number', 'has_insurance', 'get_projects')
    search_fields = ('sub_contractor_name', 'phone_number')
    list_filter = ('has_insurance',)
    inlines = [SubcontractorProjectInline]

    def get_projects(self, obj):
        """
        Display all related projects in the SubContractor list view.
        """
        return ", ".join([str(rel.project) for rel in obj.subcontractorproject_set.all()])

    get_projects.short_description = "Projects"


class DebrisAreaAdmin(admin.ModelAdmin):
    model = DebrisArea
    search_fields = ('id',)
    actions = [download_tickets_for_area]


class SiteConfigurationAdmin(admin.ModelAdmin):
    list_display = ['maintenance_mode']


admin.site.add_action(extract_text)
user_admin_site = UserAdminSite(name="ticket_dashboard")
user_admin_site.register(Ticket)

admin.site.register(SiteConfiguration)
admin.site.register(Contractor, ClientAdmin)
admin.site.register(Truck, TruckAdmin)
admin.site.register(DebrisArea, DebrisAreaAdmin)
admin.site.register(TimePunch, TimePunchAdmin),
admin.site.register(DebrisType)
admin.site.register(TicketImage, AdminTicketImages)
admin.site.register(Ticket, TicketAdmin)
admin.site.register(PDFImage)
admin.site.register(SubContractor, SubContractorAdmin)
admin.site.register(HangarTicket, HangarTicketAdmin)
admin.site.register(HangarTicketImage)
admin.site.register(FailedTicketImage, FailedTicketImageAdmin)
admin.site.register(SubcontractorProject)

